import os
import sys
import json
import importlib
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
                           QTextEdit, QPushButton, QLabel, QLineEdit, QToolButton, QGroupBox,
                           QSplitter, QMessageBox, QInputDialog, QWidget, QGridLayout,
                           QAbstractItemView, QTabWidget, QScrollArea, QFrame, QComboBox,
                           QCheckBox, QSpinBox, QFormLayout, QPlainTextEdit)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QColor, QPen

class VisualTemplateBuilder(QWidget):
    """ビジュアルテンプレートビルダー - ユーザーフレンドリーなテンプレート編集インターフェース"""

    template_changed = pyqtSignal(dict)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.template_data = {}
        self.setup_ui()

    def setup_ui(self):
        """UIのセットアップ"""
        layout = QVBoxLayout(self)

        # 説明ラベル
        info_label = QLabel("テンプレートを視覚的に編集できます。各セクションで文章を入力し、必要な場所に変数を挿入してください。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("QLabel { color: #666; font-size: 11px; margin-bottom: 10px; }")
        layout.addWidget(info_label)

        # スクロールエリア
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # メインコンテンツウィジェット
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # テンプレートセクションを作成
        self.section_editors = {}
        self.create_template_sections(content_layout)

        scroll.setWidget(content_widget)
        layout.addWidget(scroll)

        # プレビューボタン
        preview_btn = QPushButton("プレビューを表示")
        preview_btn.clicked.connect(self.show_preview)
        layout.addWidget(preview_btn)

    def create_template_sections(self, layout):
        """テンプレートセクションを作成"""
        sections = [
            ('project_title', 'プロジェクトタイトル', 'プロジェクト名の表示形式を設定します', ['project_name']),
            ('task_title', 'タスクタイトル', '各タスクのタイトル行の表示形式を設定します', ['index', 'issue_key', 'title']),
            ('task_details', 'タスク詳細', 'タスクの詳細情報の表示形式を設定します',
             ['progress', 'start_date', 'due_date', 'actual_period', 'actual_time', 'comment']),
            ('comment_history_header', 'コメント履歴ヘッダー', 'コメント履歴セクションのヘッダーを設定します', []),
            ('comment_entry', 'コメントエントリ', '各コメントの表示形式を設定します', ['timestamp', 'text']),
            ('task_separator', 'タスク区切り', 'タスク間の区切り文字を設定します', []),
            ('project_separator', 'プロジェクト区切り', 'プロジェクト間の区切り文字を設定します', [])
        ]

        for section_key, title, description, available_vars in sections:
            section_widget = self.create_section_editor(section_key, title, description, available_vars)
            layout.addWidget(section_widget)

    def create_section_editor(self, section_key, title, description, available_vars):
        """セクションエディターを作成"""
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout(group)

        # 説明
        desc_label = QLabel(description)
        desc_label.setStyleSheet("QLabel { color: #666; font-size: 10px; }")
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)

        # テキストエディター
        editor = QPlainTextEdit()
        editor.setMaximumHeight(100)
        editor.setPlaceholderText(f"{title}の内容を入力してください...")
        editor.textChanged.connect(lambda: self.on_section_changed(section_key))

        layout.addWidget(editor)

        # 変数挿入ボタン
        if available_vars:
            var_layout = QHBoxLayout()
            var_label = QLabel("利用可能な変数:")
            var_label.setStyleSheet("QLabel { font-size: 10px; color: #333; }")
            var_layout.addWidget(var_label)

            for var in available_vars:
                btn = QPushButton(f"{{{var}}}")
                btn.setMaximumWidth(100)
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #e3f2fd;
                        border: 1px solid #2196f3;
                        border-radius: 3px;
                        padding: 2px 6px;
                        font-size: 9px;
                    }
                    QPushButton:hover {
                        background-color: #bbdefb;
                    }
                """)
                btn.clicked.connect(lambda checked, v=var, e=editor: self.insert_variable(e, v))
                var_layout.addWidget(btn)

            var_layout.addStretch()
            layout.addLayout(var_layout)

        # エディターを保存
        self.section_editors[section_key] = editor

        return group

    def insert_variable(self, editor, variable):
        """変数をエディターに挿入"""
        cursor = editor.textCursor()
        cursor.insertText(f"{{{variable}}}")
        editor.setFocus()

    def on_section_changed(self, section_key):
        """セクションが変更されたときの処理"""
        editor = self.section_editors[section_key]
        content = editor.toPlainText()
        self.template_data[section_key] = content
        self.template_changed.emit(self.template_data)

    def set_template_data(self, template_data):
        """テンプレートデータを設定"""
        self.template_data = template_data.copy()

        # 各セクションエディターに内容を設定
        for section_key, editor in self.section_editors.items():
            content = template_data.get(section_key, '')
            editor.blockSignals(True)
            editor.setPlainText(content)
            editor.blockSignals(False)

    def get_template_data(self):
        """現在のテンプレートデータを取得"""
        return self.template_data.copy()

    def show_preview(self):
        """プレビューを表示"""
        preview_dialog = TemplatePreviewDialog(self.template_data, self)
        preview_dialog.exec_()

class TemplatePreviewDialog(QDialog):
    """テンプレートプレビューダイアログ"""

    def __init__(self, template_data, parent=None):
        super().__init__(parent)
        self.template_data = template_data
        self.setWindowTitle("テンプレートプレビュー")
        self.setMinimumSize(600, 500)
        self.setup_ui()

    def setup_ui(self):
        """UIのセットアップ"""
        layout = QVBoxLayout(self)

        # 説明
        info_label = QLabel("サンプルデータを使用したテンプレートのプレビューです:")
        info_label.setStyleSheet("QLabel { font-weight: bold; margin-bottom: 10px; }")
        layout.addWidget(info_label)

        # プレビューエリア
        preview_text = QTextEdit()
        preview_text.setReadOnly(True)
        preview_text.setFont(QFont("Courier New", 10))

        # サンプルデータでプレビューを生成
        preview_content = self.generate_preview()
        preview_text.setPlainText(preview_content)

        layout.addWidget(preview_text)

        # 閉じるボタン
        close_btn = QPushButton("閉じる")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)

    def generate_preview(self):
        """プレビューコンテンツを生成"""
        # サンプルデータ（数値型も含む）
        sample_data = {
            'project_name': 'サンプルプロジェクト',
            'index': 1,
            'issue_key': 'PROJ-123',
            'title': 'サンプルタスク',
            'progress': '進行中',
            'start_date': '2025/07/01',
            'due_date': '2025/07/10',
            'actual_period': '2025/07/01 ～ 2025/07/08',
            'actual_time': 8.5,  # 数値型に変更
            'estimated_time': 10.0,  # 追加
            'comment': 'サンプルコメント',
            'timestamp': '2025/07/07 14:30',
            'text': 'プログレス更新'
        }

        preview_lines = []

        # 各セクションをプレビュー
        for section_key, content in self.template_data.items():
            if content:
                try:
                    formatted_content = content.format(**sample_data)
                    preview_lines.append(f"=== {section_key} ===")
                    preview_lines.append(formatted_content)
                    preview_lines.append("")
                except (KeyError, ValueError) as e:
                    preview_lines.append(f"=== {section_key} (エラー) ===")
                    if isinstance(e, KeyError):
                        preview_lines.append(f"未定義の変数: {e}")
                    else:
                        preview_lines.append(f"フォーマットエラー: {e}")
                        # エラーの詳細情報を追加
                        preview_lines.append(f"テンプレート内容: {content[:100]}...")
                    preview_lines.append("")

        return "\n".join(preview_lines)

class TemplateEditorDialog(QDialog):
    """テンプレート編集ダイアログ"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("日報テンプレート編集 - ビジュアルエディター")
        self.setMinimumSize(1200, 800)

        # 現在のファイルの場所からパスを取得
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # ルートディレクトリを取得して正しいパスを構築
        root_dir = os.path.abspath(os.path.join(self.current_dir, "..", "..", ""))
        self.templates_path = os.path.join(root_dir, "config", "custom_templates.json")
        print(f"テンプレートエディターのパス: {self.templates_path}")

        # テンプレート変数の定義
        self.template_variables = {
            'プロジェクト': ['project_name'],
            'タスク': ['title', 'issue_key', 'start_date', 'due_date', 'progress',
                      'actual_start_date', 'actual_end_date', 'actual_period',
                      'estimated_time', 'actual_time', 'comment', 'source', 'index'],
            'コメント': ['timestamp', 'text']
        }

        # テンプレートセクション
        self.template_sections = [
            'project_title', 'task_title', 'task_details',
            'comment_history_header', 'comment_entry',
            'task_separator', 'project_separator'
        ]

        # 編集中のテンプレート
        self.current_template = None
        self.current_section = None

        # カスタムテンプレートのロード
        self.templates = self.load_templates()

        self.setup_ui()
        
    def setup_ui(self):
        """UIのセットアップ"""
        main_layout = QVBoxLayout(self)

        # 上部のツールバー
        toolbar_layout = QHBoxLayout()

        # 新規テンプレート作成ボタン
        self.new_template_btn = QPushButton("新規テンプレート")
        self.new_template_btn.clicked.connect(self.create_new_template)
        self.new_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        toolbar_layout.addWidget(self.new_template_btn)

        # テンプレート名入力フィールド
        self.template_name_edit = QLineEdit()
        self.template_name_edit.setPlaceholderText("テンプレート名")
        self.template_name_edit.setEnabled(False)
        self.template_name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
        """)
        toolbar_layout.addWidget(self.template_name_edit)

        # 保存ボタン
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_current_template)
        self.save_btn.setEnabled(False)
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        toolbar_layout.addWidget(self.save_btn)

        # 削除ボタン
        self.delete_btn = QPushButton("削除")
        self.delete_btn.clicked.connect(self.delete_current_template)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        toolbar_layout.addWidget(self.delete_btn)

        # 編集モード切り替えボタン
        self.mode_toggle_btn = QPushButton("上級者モード (JSON)")
        self.mode_toggle_btn.clicked.connect(self.toggle_edit_mode)
        self.mode_toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        toolbar_layout.addWidget(self.mode_toggle_btn)

        toolbar_layout.addStretch()

        main_layout.addLayout(toolbar_layout)

        # メイン編集エリア
        splitter = QSplitter(Qt.Horizontal)

        # 左側のテンプレートリスト
        template_list_group = QGroupBox("テンプレート")
        template_list_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        template_list_layout = QVBoxLayout()

        self.template_list = QListWidget()
        self.template_list.setSelectionMode(QAbstractItemView.SingleSelection)
        self.template_list.currentItemChanged.connect(self.on_template_selected)
        self.template_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)
        self.update_template_list()

        template_list_layout.addWidget(self.template_list)
        template_list_group.setLayout(template_list_layout)
        splitter.addWidget(template_list_group)

        # 中央の編集エリア - タブ形式で視覚的エディターとJSONエディターを切り替え
        self.editor_tabs = QTabWidget()
        self.editor_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #2196F3;
            }
        """)

        # ビジュアルエディター
        self.visual_editor = VisualTemplateBuilder()
        self.visual_editor.template_changed.connect(self.on_visual_editor_changed)
        self.editor_tabs.addTab(self.visual_editor, "📝 ビジュアルエディター")

        # JSONエディター（上級者向け）
        json_editor_widget = QWidget()
        json_editor_layout = QVBoxLayout(json_editor_widget)

        # 警告ラベル
        warning_label = QLabel("⚠️ 上級者向け: JSONフォーマットで直接編集します。構文エラーにご注意ください。")
        warning_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                color: #856404;
                padding: 8px;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                margin-bottom: 10px;
            }
        """)
        json_editor_layout.addWidget(warning_label)

        self.json_editor = QTextEdit()
        self.json_editor.setFont(QFont("Courier New", 10))
        self.json_editor.setLineWrapMode(QTextEdit.NoWrap)
        self.json_editor.textChanged.connect(self.on_json_editor_changed)
        self.json_editor.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f8f9fa;
                font-family: 'Courier New', monospace;
            }
        """)

        json_editor_layout.addWidget(self.json_editor)
        self.editor_tabs.addTab(json_editor_widget, "🔧 JSONエディター")

        splitter.addWidget(self.editor_tabs)
        
        # 右側の変数と説明
        vars_widget = QWidget()
        vars_layout = QVBoxLayout(vars_widget)
        
        # 変数挿入ボタン
        vars_group = QGroupBox("テンプレート変数（クリックで挿入）")
        vars_group_layout = QVBoxLayout()
        
        for category, variables in self.template_variables.items():
            cat_group = QGroupBox(category)
            cat_layout = QGridLayout()
            
            for i, var in enumerate(variables):
                btn = QPushButton(f"{{{var}}}")
                btn.setToolTip(f"クリックで「{{{var}}}」を挿入")
                btn.clicked.connect(lambda checked, v=var: self.insert_variable(v))
                row, col = divmod(i, 2)
                cat_layout.addWidget(btn, row, col)
            
            cat_group.setLayout(cat_layout)
            vars_group_layout.addWidget(cat_group)
        
        vars_group_layout.addStretch()
        vars_group.setLayout(vars_group_layout)
        vars_layout.addWidget(vars_group)
        
        # 説明
        help_group = QGroupBox("説明")
        help_layout = QVBoxLayout()
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h3>テンプレート編集のヘルプ</h3>
        <p>このダイアログでは、日報の出力形式をカスタマイズできます。</p>
        <p><b>使い方：</b></p>
        <ol>
            <li>左側からテンプレートを選択するか、新規作成します</li>
            <li>編集エリアでJSONフォーマットのテンプレートを編集します</li>
            <li>右側の変数ボタンをクリックすると、その変数が挿入されます</li>
            <li>編集が完了したら「保存」ボタンをクリックします</li>
        </ol>
        <p><b>テンプレート構造：</b></p>
        <ul>
            <li><b>project_title</b> - プロジェクト名の表示形式（{project_name}を使用）</li>
            <li><b>task_title</b> - 各タスクのタイトル行の形式</li>
            <li><b>task_details</b> - タスクの詳細情報の表示形式</li>
            <li><b>comment_history_header</b> - コメント履歴セクションのヘッダー</li>
            <li><b>comment_entry</b> - 各コメントの表示形式</li>
            <li><b>task_separator</b> - タスク間の区切り文字</li>
            <li><b>project_separator</b> - プロジェクト間の区切り文字</li>
        </ul>
        <p><b>重要：</b></p>
        <ul>
            <li>テンプレートはJSONフォーマットで編集してください</li>
            <li>改行は\\nで表現し、インデントはスペースで指定します</li>
            <li>変数は{variable_name}の形式で記述します</li>
            <li>文字列内でダブルクォートを使用する場合は\\"でエスケープしてください</li>
        </ul>
        """)
        help_layout.addWidget(help_text)
        help_group.setLayout(help_layout)
        vars_layout.addWidget(help_group)
        
        splitter.addWidget(vars_widget)
        
        # スプリッターの初期サイズ比率を設定
        splitter.setSizes([200, 500, 300])
        
        main_layout.addWidget(splitter)
        
        # ボタンエリア
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("閉じる")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        main_layout.addLayout(button_layout)

    def toggle_edit_mode(self):
        """編集モードを切り替え"""
        current_index = self.editor_tabs.currentIndex()
        if current_index == 0:  # ビジュアルエディターから JSONエディターへ
            self.editor_tabs.setCurrentIndex(1)
            self.mode_toggle_btn.setText("ビジュアルモード")
        else:  # JSONエディターから ビジュアルエディターへ
            self.editor_tabs.setCurrentIndex(0)
            self.mode_toggle_btn.setText("上級者モード (JSON)")

    def on_visual_editor_changed(self, template_data):
        """ビジュアルエディターが変更されたときの処理"""
        if not self.current_template:
            return

        # テンプレートデータを更新
        self.templates[self.current_template] = template_data

        # JSONエディターも同期更新
        self.update_json_editor()

    def on_json_editor_changed(self):
        """JSONエディターが変更されたときの処理"""
        if not self.current_template:
            return

        # JSONフォーマットの内容をパース
        content = self.json_editor.toPlainText()
        try:
            template_data = json.loads(content)

            # テンプレートデータを更新
            if isinstance(template_data, dict):
                self.templates[self.current_template] = template_data

                # ビジュアルエディターも同期更新
                self.visual_editor.set_template_data(template_data)
        except json.JSONDecodeError:
            # JSONパースエラーの場合は何もしない（編集中の可能性）
            pass

    def update_json_editor(self):
        """JSONエディターの内容を更新"""
        if not self.current_template:
            return

        template = self.templates.get(self.current_template, {})
        content = json.dumps(template, ensure_ascii=False, indent=4)

        # エディタの更新を一時的にブロック
        self.json_editor.blockSignals(True)
        self.json_editor.setText(content)
        self.json_editor.blockSignals(False)

    def load_templates(self):
        """テンプレートをJSONファイルから読み込む"""
        from config.report_templates import AVAILABLE_TEMPLATES
        
        # デフォルトテンプレートをコピー
        templates = AVAILABLE_TEMPLATES.copy()
        
        # カスタムテンプレートをロード（存在する場合）
        try:
            if os.path.exists(self.templates_path):
                with open(self.templates_path, 'r', encoding='utf-8') as f:
                    custom_templates = json.load(f)
                    templates.update(custom_templates)
        except Exception as e:
            print(f"テンプレート読み込みエラー: {e}")
        
        return templates
    
    def save_templates(self):
        """カスタムテンプレートをJSONファイルに保存"""
        
        # デフォルトテンプレートは保存しない
        custom_templates = {}
        for name, template in self.templates.items():
            if name not in ['デフォルト', 'カスタム1']:
                custom_templates[name] = template
        
        # JSONファイルに保存
        try:
            # ディレクトリが存在するか確認
            os.makedirs(os.path.dirname(self.templates_path), exist_ok=True)
            
            print(f"テンプレート保存先: {self.templates_path}")
            print(f"保存するテンプレート: {list(custom_templates.keys())}")
            
            with open(self.templates_path, 'w', encoding='utf-8') as f:
                json.dump(custom_templates, f, ensure_ascii=False, indent=4)
            
            print(f"{len(custom_templates)}個のカスタムテンプレートを保存しました")
            
            # 設定ファイルを再読み込みして保存したことを確認
            import importlib
            from config import report_templates
            if 'config.report_templates' in sys.modules:
                del sys.modules['config.report_templates']
            importlib.reload(report_templates)
            report_templates.load_custom_templates()
            
        except Exception as e:
            QMessageBox.critical(self, "保存エラー", f"テンプレートの保存に失敗しました。\n\nエラー詳細: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_template_list(self):
        """テンプレートリストを更新"""
        self.template_list.clear()
        
        for name in sorted(self.templates.keys()):
            item = QListWidgetItem(name)
            item.setData(Qt.UserRole, name)
            
            # デフォルトテンプレートは編集不可
            if name in ['デフォルト', 'カスタム1']:
                item.setFlags(item.flags() & ~Qt.ItemIsEnabled)
            
            self.template_list.addItem(item)
    
    def on_template_selected(self, current, _previous):
        """テンプレートが選択されたときの処理"""
        if not current:
            self.current_template = None
            self.template_name_edit.setText("")
            self.template_name_edit.setEnabled(False)
            self.save_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.visual_editor.setEnabled(False)
            self.json_editor.setEnabled(False)
            self.mode_toggle_btn.setEnabled(False)
            return

        template_name = current.data(Qt.UserRole)
        self.current_template = template_name

        # デフォルトテンプレートは編集不可
        is_editable = template_name not in ['デフォルト', 'カスタム1']

        self.template_name_edit.setText(template_name)
        self.template_name_edit.setEnabled(is_editable)
        self.save_btn.setEnabled(is_editable)
        self.delete_btn.setEnabled(is_editable)
        self.visual_editor.setEnabled(is_editable)
        self.json_editor.setEnabled(is_editable)
        self.mode_toggle_btn.setEnabled(is_editable)

        # テンプレートデータを取得
        template = self.templates.get(template_name, {})

        # ビジュアルエディターにテンプレートデータを設定
        self.visual_editor.set_template_data(template)

        # JSONエディターにもテンプレートデータを設定
        self.update_json_editor()

    def insert_variable(self, variable):
        """変数をJSONエディタに挿入（レガシー対応）"""
        if self.json_editor.isEnabled() and self.editor_tabs.currentIndex() == 1:
            self.json_editor.insertPlainText(f"{{{variable}}}")
            self.json_editor.setFocus()

    def get_section_display_name(self, section):
        """セクション名の表示名を取得"""
        display_names = {
            'project_title': 'プロジェクトタイトル',
            'task_title': 'タスクタイトル',
            'task_details': 'タスク詳細',
            'comment_history_header': 'コメント履歴ヘッダー',
            'comment_entry': 'コメントエントリ',
            'task_separator': 'タスク区切り',
            'project_separator': 'プロジェクト区切り'
        }
        return display_names.get(section, section)
    
    def create_new_template(self):
        """新規テンプレートの作成"""
        # 新しいテンプレート名を取得
        name, ok = QInputDialog.getText(self, "新規テンプレート", "テンプレート名を入力してください:")
        
        if ok and name:
            # 同名のテンプレートがないかチェック
            if name in self.templates:
                QMessageBox.warning(self, "警告", f"「{name}」という名前のテンプレートは既に存在します。")
                return
            
            # デフォルトテンプレートをコピー
            from config.report_templates import DEFAULT_TEMPLATE
            self.templates[name] = DEFAULT_TEMPLATE.copy()
            
            # リストを更新して新しいテンプレートを選択
            self.update_template_list()
            
            # 新しいテンプレートを選択
            for i in range(self.template_list.count()):
                item = self.template_list.item(i)
                if item.data(Qt.UserRole) == name:
                    self.template_list.setCurrentItem(item)
                    break
    def save_current_template(self):
        """現在のテンプレートを保存"""
        if not self.current_template:
            return

        # テンプレート名が変更されているかチェック
        new_name = self.template_name_edit.text().strip()

        if not new_name:
            QMessageBox.warning(self, "警告", "テンプレート名を入力してください。")
            return

        # 現在のエディターからテンプレートデータを取得
        if self.editor_tabs.currentIndex() == 0:  # ビジュアルエディター
            template_data = self.visual_editor.get_template_data()
        else:  # JSONエディター
            try:
                content = self.json_editor.toPlainText()
                template_data = json.loads(content)
            except json.JSONDecodeError as e:
                QMessageBox.critical(self, "JSONエラー", f"JSONフォーマットにエラーがあります:\n{str(e)}")
                return

        # テンプレート構造を検証
        missing_sections = []
        for required_section in self.template_sections:
            if required_section not in template_data or not template_data[required_section]:
                missing_sections.append(self.get_section_display_name(required_section))

        if missing_sections:
            reply = QMessageBox.question(
                self,
                "テンプレート検証",
                f"以下のセクションが空または未定義です:\n- {'\n- '.join(missing_sections)}\n\n保存を続行しますか？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        # project_title で使われる変数を検証
        project_title = template_data.get('project_title', '')
        if '{title}' in project_title or '{issue_key}' in project_title:
            reply = QMessageBox.question(
                self,
                "テンプレート検証",
                "プロジェクトタイトルセクションに、タスクレベルの変数 (title, issue_key など) が使用されています。\n"
                "これらの変数はプロジェクトレベルでは利用できず、エラーになる可能性があります。\n\n"
                "修正して保存しますか？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                # 不正な変数を置換
                template_data['project_title'] = template_data['project_title'].replace('{title}', '{project_name}')
                template_data['project_title'] = template_data['project_title'].replace('{issue_key}', '')

                # 修正されたデータを両方のエディターに反映
                self.visual_editor.set_template_data(template_data)
                self.update_json_editor()

        # テンプレートデータを更新
        self.templates[self.current_template] = template_data

        if new_name != self.current_template:
            # 同名のテンプレートがないかチェック
            if new_name in self.templates:
                QMessageBox.warning(self, "警告", f"「{new_name}」という名前のテンプレートは既に存在します。")
                return

            # テンプレート名を変更
            self.templates[new_name] = self.templates[self.current_template]
            del self.templates[self.current_template]
            self.current_template = new_name

        # テンプレートをファイルに保存
        self.save_templates()

        # リストを更新して現在のテンプレートを選択
        self.update_template_list()

        # 保存したテンプレートを選択
        for i in range(self.template_list.count()):
            item = self.template_list.item(i)
            if item.data(Qt.UserRole) == self.current_template:
                self.template_list.setCurrentItem(item)
                break

        QMessageBox.information(self, "保存完了", "テンプレートを保存しました。")
    
    def delete_current_template(self):
        """現在のテンプレートを削除"""
        if not self.current_template:
            return
        
        # デフォルトテンプレートは削除不可
        if self.current_template in ['デフォルト', 'カスタム1']:
            QMessageBox.warning(self, "警告", "デフォルトテンプレートは削除できません。")
            return
        
        # 確認ダイアログ
        reply = QMessageBox.question(
            self, "削除確認", 
            f"テンプレート「{self.current_template}」を削除してもよろしいですか？",
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # テンプレートを削除
            del self.templates[self.current_template]
            
            # テンプレートをファイルに保存
            self.save_templates()
            
            # リストを更新
            self.update_template_list()
            
            # 選択をクリア
            self.template_list.clearSelection()
            self.current_template = None
            
            QMessageBox.information(self, "削除完了", "テンプレートを削除しました。")
